import { getShortPointName } from '@/lib/utils/getShortPointName'
import { Avatar, Button, Card, CardBody, CardHeader, Chip, Input, Spinner, Textarea, User } from "@heroui/react"
import { Link, useMatch, useRouter } from '@tanstack/react-router'
import { CasePartialWithRelations } from '../../../server/prisma/generated/zod/modelSchema'
import { Timeline } from '@/components/Timeline'
import { PointType, Way } from '@/types'
import { trpc } from '@/trpc'
import { useUserStore } from '@/store'
import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import { UserRating } from '@/components/UserRating'
import { PhoneInput } from '@/components/PhoneInput'

import { parsePhoneNumber } from 'react-phone-number-input/input'
import { MainMap } from '@/components/map/MainMap'
import L, { Map as LMap } from 'leaflet'
import { <PERSON>er, Popup, useMap } from 'react-leaflet'
import { Polyline } from 'react-leaflet/Polyline'

import _fromIcon from '@/assets/from.png'
import { useTranslation } from 'react-i18next'
import { RatingForm } from '@/components/RatingForm'
import { pointColorByType } from '@/lib/utils/pointColorByType'
import IconMessage from '@/lib/svg/MessageIcon'
import { statusColorByName } from '@/lib/utils/statusColor'
import IconPencil from '@/lib/svg/IconPencil'

export const CasePage = () => {
  const { t } = useTranslation()

  const { params } = useMatch({ from: '/route/$caseId' })
  const { caseId } = params
  const { data: userData } = useUserStore()

  const { isFetching, data, isError, error, refetch } = trpc.case.id.useQuery(caseId)

  const { data: sendedRatings } = trpc.users.sendedRatings.useQuery({
    wherePayload: {
      caseId: data?.id,
      senderId: userData?.id
    }
  })

  const [isAssigned, setIsAssigned] = useState(false)
  const [isAuthor, setIsAuthor] = useState<boolean | undefined>()

  const [mapInstance, setMapInstance] = useState<LMap>()
  const [pointsArr, setPointsArr] = useState<any[]>([])

  const [showRatingForm, setShowRatingForm] = useState(false)
  // const caseMap = useMap()

  const {
    isPending: assign_isLoading,
    isSuccess: assign_isSuccess,
    isError: assign_isError,
    error: assign_error,
    data: assign_data,
    mutateAsync: assignClient
  } = trpc.case.assignClient.useMutation()

  useEffect(() => {
    setIsAssigned(!!data?.clients.find((x) => x.id === userData?.id))
    const validPoints = [data?.from, ...(data?.middlepoints ?? []), data?.to]
      .filter((point) => point && point.lat != null && point.lon != null)
      .map((point) => [Number(point.lat), Number(point.lon)])
    setPointsArr(validPoints)
    setIsAuthor(data?.authorId === userData?.id)
  }, [data])

  useEffect(() => {
    if (data?.status != 'OPEN' && isAssigned && !isAuthor) {
      setShowRatingForm(true)
    }
  }, [isAssigned, data])

  useEffect(() => {
    assign_data && setIsAssigned(assign_data?.isAssigned)
  }, [assign_data])

  const dotColorType = {
    from: 'primary',
    middlepoints: 'warning',
    to: 'success'
  }

  // useEffect(() => {
  //   if (assign_isError) {
  //     toast.error(t('Server error'))
  //   }
  //   if (assign_isSuccess) {
  //     assign_data.isAssigned ? toast(t('Request sended')) : toast(t('Request cancelled'))
  //     refetch()
  //   }
  // }, [assign_isError, assign_isSuccess])

  async function assignClientHandler() {
    await assignClient(String(data?.id))
  }

  function clickPointHandler(point: Way): void {
    mapInstance?.flyTo([point.lat, point.lon], 15, {
      duration: 3
    })
  }

  const PointBody = ({ point, type }: { point: Way; type: PointType }) => {
    return (
      <Card className='w-full' radius='sm' shadow='none'>
        <CardHeader className='flex justify-between'>
          <Chip classNames={{ content: 'text-left' }} color={dotColorType[type]} variant='dot'>
            <span className='text-lg text-left text-ellipsis'>{getShortPointName(point, type)}</span>{' '}
          </Chip>
          <Chip variant='solid' className='font-bold'>
            <span className='font-semibold'>{point.date?.toLocaleDateString()}</span>
          </Chip>
        </CardHeader>

        <CardBody>
          <div className='cursor-pointer space-y-4'>
            <div>
              {/* <div className='text-sm'>{point.geometa?.display_name}</div> */}
              <Textarea
                onClick={() => clickPointHandler(point)}
                label={t('Full address')}
                size='lg'
                radius='sm'
                labelPlacement='outside'
                readOnly
                className='w-full shadow-none'
                classNames={{
                  input: 'w-full shadow-none',
                  inputWrapper: 'shadow-none'
                }}
                value={point.geometa?.display_name}
              />
              {/* <div className='text-default-500'>{[point.lat, point.lon].join(', ')}</div> */}
            </div>
            {point.comment && (
              <div>
                <Textarea
                  label={t('Note')}
                  size='md'
                  labelPlacement='outside'
                  readOnly
                  className='w-full'
                  classNames={{
                    input: 'w-full',
                    inputWrapper: 'shadow-none'
                  }}
                  value={point.comment}
                />
              </div>
            )}
          </div>
        </CardBody>
        {/* <CardFooter className=' justify-between'>
          <div className='text-sm'>{point.geometa?.display_name}</div>
        </CardFooter> */}
      </Card>
    )
  }

  const MapInstance = () => {
    const map = useMap()

    useEffect(() => {
      if (pointsArr.length > 0) {
        try {
          const bounds = new L.LatLngBounds(pointsArr)
          map.fitBounds(bounds)
        } catch (error) {
          console.warn('Error fitting bounds:', error)
        }
      }
    }, [pointsArr, map])

    useEffect(() => {
      setMapInstance(map)
    }, [map])

    return <></>
  }

  return (
    <div>
      <div>
        {data?.id && (
          <div className='grid lg:grid-cols-2 lg:gap-7'>
            <div className='mb-5'>
              <div className={`bg-default-100 dark:bg-default-50 p-2 rounded-lg mb-5 flex justify-between items-center`}>
                <div>
                  <User
                    name={data.author.username}
                    // description={isAssigned ? data.author.email : ''}
                    description={
                      <div>
                        <UserRating user={data.author} />
                        {isAssigned && data.status == 'OPEN' && (
                          <div className='mt-2 text-default-600'>
                            <div className='text-sm'>
                              <a href={'tel:' + data?.author.phone}>{parsePhoneNumber(data.author.phone)?.format('NATIONAL')}</a>
                            </div>
                            <div className='text-sm'>
                              <a href={'email:' + data?.author.email}>{data.author.email}</a>
                            </div>
                          </div>
                        )}
                      </div>
                    }
                    avatarProps={{
                      src: data.author.avatar?.base64string,
                      size: isAssigned ? 'lg' : 'md'
                    }}
                  />
                </div>
                {data.status == 'OPEN' && (
                  <Button onClick={assignClientHandler} isLoading={assign_isLoading} color='primary' radius='sm' variant='flat'>
                    {isAssigned ? t('Cancel request') : t('Send request')}
                  </Button>
                )}

                {isAssigned && !isAuthor && (
                  <Button isIconOnly>
                    <Link to='/messages' search={{ partnerId: data?.authorId }}>
                      <IconMessage className='w-6 h-6' />
                    </Link>
                  </Button>
                )}
              </div>

              {showRatingForm && (
                <div className='mt-5 mb-10'>
                  <RatingForm initValue={sendedRatings?.[0]} c={data} targetUser={data.author} />
                </div>
              )}
              <div className='relative pl-3'>
                <div className='flex justify-between mb-2'>
                  <div className='text-default-500 text-sm'>#{data.id.slice(0, 8)}</div>
                  {/* <Chip variant='flat' color={data.price ? 'danger' : 'default'}>
                    {t('Fee')}: {data.price}
                  </Chip> */}
                  <Chip variant='flat' className='font-bold' color={statusColorByName(data.status)}>
                    {data.status}
                  </Chip>

                  {isAuthor && (
                    <Button variant='flat'  isIconOnly>
                      <Link className='flex items-center' to='/edit' search={{ caseId: data?.id }}>
                        {/* <span className='capitalize'>{t("edit")}</span> */}
                        <IconPencil className='w-5 h-5' />
                      </Link>
                    </Button>
                  )}
                </div>

                
                <div className='space-y-5'>
                  <div>
                    <h2 className='font-semibold text-primary-500 mb-2'>{t('From')}</h2>
                    <PointBody point={data.from} type='from' />
                  </div>
                  {data.middlepoints.map((point) => (
                    <div key={point.id}>
                      <h2 className='font-semibold text-warning-500 mb-2'>{t('Via')}</h2>
                      <PointBody point={point} type='middlepoints' />
                    </div>
                  ))}
                  <div>
                    <h2 className='font-semibold text-secondary-500 mb-2'>{t('To')}</h2>
                    <PointBody point={data.to} type='to' />
                  </div>
                </div>
              </div>
            </div>
            <div className='flex gap-5 flex-col lg:flex-col-reverse lg:justify-end'>
              <div className=''>
                {data.description && (
                  <Textarea
                    label={t('Route description')}
                    size='md'
                    labelPlacement='outside'
                    readOnly
                    className='w-full'
                    classNames={{
                      input: 'w-full'
                    }}
                    value={data.description}
                  />
                )}
              </div>
              <Card
                classNames={{
                  body: 'p-0'
                }}
              >
                <CardBody>
                  <MainMap mapStyles={{ height: '430px' }} readOnly initBrowserGeocode={false}>
                    <MapInstance />
                    <Polyline positions={pointsArr} />
                    {data?.from?.lat != null && data?.from?.lon != null && (
                      <Marker
                        icon={L.divIcon({ className: `w-4 h-4 ${pointColorByType('from')} rounded-full` })}
                        position={[Number(data.from.lat), Number(data.from.lon)]}
                      >
                        <Popup>
                          <div>{data.from?.geometa?.display_name}</div>
                        </Popup>
                      </Marker>
                    )}
                    {data?.middlepoints?.filter(point => point.lat != null && point.lon != null).map((point) => (
                      <Marker
                        key={point.id}
                        icon={L.divIcon({ className: `w-4 h-4 ${pointColorByType('middlepoints')} rounded-full` })}
                        position={[Number(point.lat), Number(point.lon)]}
                      >
                        <Popup>
                          <div>{point.geometa?.display_name}</div>
                        </Popup>
                      </Marker>
                    ))}
                    {data?.to?.lat != null && data?.to?.lon != null && (
                      <Marker
                        icon={L.divIcon({ className: `w-4 h-4 ${pointColorByType('to')} rounded-full` })}
                        position={[Number(data.to.lat), Number(data.to.lon)]}
                      >
                        <Popup>{data.to?.geometa?.display_name}</Popup>
                      </Marker>
                    )}
                  </MainMap>
                </CardBody>
              </Card>
            </div>
          </div>
        )}
      </div>
      {isFetching && (
        <div className='h-screen flex flex-col items-center'>
          <Spinner />
        </div>
      )}
    </div>
  )
}
