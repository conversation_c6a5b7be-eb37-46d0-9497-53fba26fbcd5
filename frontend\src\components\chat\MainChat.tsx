import { memo, useMemo } from 'react'
import { <PERSON>, Card<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ScrollShadow } from "@heroui/react"
import { useTranslation } from 'react-i18next'
import { MessageSkeleton } from './MessageSkeleton'
import { MessageItem } from './MessageItem'
import { ChatInput } from './ChatInput'
import { ChatHeader } from './ChatHeader'
import { useChat } from '@/hooks/useChat'
import { ChatPartner } from '@/types/chat'

interface Props {
  activeUser?: ChatPartner
}

const MainChat = memo(({ activeUser }: Props) => {
  const { t } = useTranslation()

  const {
    chatState,
    inputValue,
    setInputValue,
    sendMessage,
    chatContainerRef,
    sessionUser,
    isSending,
    isLoading
  } = useChat({
    partnerId: activeUser?.id,
    enabled: !!activeUser?.id
  })

  // Memoize messages to prevent unnecessary re-renders
  const memoizedMessages = useMemo(() => chatState.messages, [chatState.messages])

  // Memoize loading skeletons
  const loadingSkeletons = useMemo(() => (
    <div className="space-y-3">
      {[...Array(3)].map((_, index) => (
        <div key={index} className={`flex ${index % 2 === 0 ? 'justify-start' : 'justify-end'}`}>
          <MessageSkeleton />
        </div>
      ))}
    </div>
  ), [])

  // Memoize empty states
  const emptyState = useMemo(() => {
    if (!activeUser?.id) {
      return <div className="text-center text-default-600">{t('Choose user')}</div>
    }
    if (memoizedMessages.length === 0 && !isLoading) {
      return <div className="text-center text-default-600">{t('No messages')}</div>
    }
    return null
  }, [activeUser?.id, memoizedMessages.length, isLoading, t])

  // Memoize chat input props
  const chatInputProps = useMemo(() => ({
    value: inputValue,
    onChange: setInputValue,
    onSend: sendMessage,
    isLoading: isSending,
    isDisabled: !activeUser?.id,
    placeholder: activeUser?.id ? t('Input text here') : t('Select a user to start messaging')
  }), [inputValue, setInputValue, sendMessage, isSending, activeUser?.id, t])

  return (
    <Card shadow="lg" className="h-full flex flex-col">
      <CardBody className="flex-1 flex flex-col p-0">
        {/* Chat Header */}
        <div className="p-4 border-b border-default-200">
          <ChatHeader partner={activeUser} />
        </div>

        {/* Messages Container */}
        <div className="flex-1 flex flex-col min-h-0">
          {isLoading && activeUser?.id && (
            <div className="p-4">
              {loadingSkeletons}
            </div>
          )}

          <ScrollShadow
            ref={chatContainerRef}
            hideScrollBar
            className="flex-1 p-4"
            style={{ maxHeight: '60vh' }}
          >
            <div className="space-y-4">
              {memoizedMessages.map((message) => (
                <MessageItem
                  key={message.id}
                  message={message}
                  isOwn={message.senderId === sessionUser?.id}
                  sessionUserId={sessionUser?.id}
                />
              ))}

              {/* Typing indicator - можно добавить позже */}
              {/* {isPartnerTyping && (
                <div className="flex justify-start">
                  <div className="bg-default-100 rounded-lg px-3 py-2">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-default-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-default-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-default-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                </div>
              )} */}
            </div>
          </ScrollShadow>

          {/* Empty State */}
          {emptyState && (
            <div className="flex-1 flex items-center justify-center p-8">
              {emptyState}
            </div>
          )}
        </div>
      </CardBody>

      {/* Chat Input */}
      <CardFooter className="p-4 border-t border-default-200">
        <ChatInput {...chatInputProps} />
      </CardFooter>
    </Card>
  )
})

MainChat.displayName = 'MainChat'

export { MainChat }
