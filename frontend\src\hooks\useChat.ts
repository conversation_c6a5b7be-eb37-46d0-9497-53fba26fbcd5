import { useCallback, useEffect, useRef, useState } from 'react'
import { trpc } from '@/trpc'
import { toast } from 'react-toastify'
import { useTranslation } from 'react-i18next'
import { ChatMessage, SendMessageRequest, ChatState } from '@/types/chat'
import { useUserStore } from '@/store'
import useBus from 'use-bus'

interface UseChatOptions {
  partnerId?: string
  enabled?: boolean
}

export const useChat = ({ partnerId, enabled = true }: UseChatOptions) => {
  const { t } = useTranslation()
  const { data: sessionUser } = useUserStore()
  const [inputValue, setInputValue] = useState('')
  const chatContainerRef = useRef<HTMLDivElement>(null)
  
  // Queries
  const {
    data: messages = [],
    isLoading,
    isError,
    refetch,
    isFetching
  } = trpc.message.messagesByPartner.useQuery(partnerId!, {
    enabled: enabled && !!partnerId,
    refetchOnWindowFocus: false,
    staleTime: 30000, // 30 seconds
  })

  // Mutations
  const sendMessageMutation = trpc.message.send.useMutation({
    onSuccess: () => {
      setInputValue('')
      refetch()
      // Scroll to bottom after a short delay to ensure new message is rendered
      setTimeout(scrollToBottom, 150)
      toast.success(t('Message sent'), {
        className: 'bg-success-50 text-success-900 font-semibold text-sm',
        autoClose: 2000
      })
    },
    onError: (error) => {
      toast.error(t('Failed to send message'), {
        className: 'bg-danger-50 text-danger-900 font-semibold text-sm',
        autoClose: 6000
      })
      console.error('Send message error:', error)
    }
  })

  // Listen for new messages
  useBus('NEW_MESSAGE', () => {
    if (partnerId) {
      refetch()
    }
  })

  const sendMessage = useCallback(async () => {
    const content = inputValue.trim()
    
    if (!content) {
      toast.error(t('Please enter a message'))
      return
    }

    if (!partnerId) {
      toast.error(t('Please select a recipient'))
      return
    }

    try {
      await sendMessageMutation.mutateAsync({
        content,
        receiverId: partnerId
      })
    } catch (error) {
      // Error is handled in mutation onError
    }
  }, [inputValue, partnerId, sendMessageMutation, t])

  const scrollToBottom = useCallback(() => {
    if (chatContainerRef.current) {
      const container = chatContainerRef.current
      container.scrollTo({
        top: container.scrollHeight,
        behavior: 'smooth'
      })
    }
  }, [])

  // Auto-scroll on new messages
  useEffect(() => {
    if (messages.length > 0) {
      // Small delay to ensure DOM is updated
      const timer = setTimeout(scrollToBottom, 100)
      return () => clearTimeout(timer)
    }
  }, [messages.length, scrollToBottom])

  // Auto-scroll when partner changes
  useEffect(() => {
    if (partnerId && messages.length > 0) {
      scrollToBottom()
    }
  }, [partnerId, scrollToBottom])

  const chatState: ChatState = {
    messages: messages || [],
    isLoading,
    isSending: sendMessageMutation.isPending,
    error: isError ? 'Failed to load messages' : null,
    hasMore: false // TODO: implement pagination
  }

  return {
    // State
    chatState,
    inputValue,
    sessionUser,
    
    // Refs
    chatContainerRef,
    
    // Actions
    setInputValue,
    sendMessage,
    scrollToBottom,
    refetch,
    
    // Status
    isSending: sendMessageMutation.isPending,
    isLoading: isLoading || isFetching,
    isError
  }
}
