import { trpc } from '@/trpc'
import { <PERSON><PERSON>, Card, Skeleton, User } from "@heroui/react"
import { PartnerSkeleton } from './PartnerSkeleton'
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/types/chat'
import { useEffect, useState } from 'react'
import { useRouter } from '@tanstack/react-router'
import useBus from 'use-bus'

export const ChatPartners = ({ setUser, initUserId }: { initUserId?: string; setUser?: (user: <PERSON><PERSON><PERSON><PERSON><PERSON>) => void }) => {
  // const [fUser, setfUser] = useState<UserType>()
  const [isSearch, setIsSearch] = useState(false)
  const router = useRouter()

  const { data, isFetching, isError, isLoading, isSuccess, refetch } = trpc.message.partners.useQuery()

  const {
    data: partner_data,
    // isFetching: partner_isFetching,
    isSuccess: partner_isSuccess,
    isError: partner_isError
  } = trpc.message.getPartnerById.useQuery(initUserId, {
    enabled: !!(isSearch && initUserId)
  })

  useBus('NEW_MESSAGE', () => {
    if (!data?.find((x) => x.id === initUserId)) {
      refetch()
    }
  })

  useEffect(() => {
    if (partner_isSuccess && partner_data) {
      setUser?.(partner_data)
      if (!data?.find((x) => x.id === partner_data.id)) {
        data?.unshift(partner_data)
      }
    }
  }, [partner_isSuccess, partner_data])

  useEffect(() => {
    if (isSuccess && initUserId) {
      const fu = data.find((x) => x.id == initUserId)
      if (fu) {
        setUser?.(fu)
        window.scrollTo({
          top: document.documentElement.scrollHeight,
          behavior: 'smooth'
        })
      } else {
        setIsSearch(true)
      }
    }

    if (!initUserId && !isSearch && isSuccess && data?.length > 0) {
      setUser?.(data[0])
      // setfUser(data[0])
    }
  }, [isSuccess, data])

  function choosePartner(partner: Partner) {
    setUser?.(partner)
    router.navigate({
      to: '/messages',
      search: {
        partnerId: partner.id
      }
    })
  }

  return (
    <>
      <div className='space-x-3 lg:space-x-0 lg:space-y-3 flex lg:block items-center'>
        {!isLoading &&
          data?.map((partner) => (
            <div
              //d
              key={partner.id}
              onClick={() => choosePartner(partner)}
              className='shrink-0 cursor-pointer bg-default-50 hover:bg-default-100 py-2 rounded-md px-2 transition-all duration-150'
            >
              <Badge isInvisible={partner?._count?.sentMessages === 0} placement='top-left' content={partner?._count?.sentMessages} color='danger' shape='circle'>
                <User
                  className='text-ellipsis'
                  classNames={{
                    name: 'text-default-900 text-ellipsis'
                  }}
                  name={partner?.username}
                  //   description={<div>{partner.email}</div>}
                  avatarProps={{
                    src: partner?.avatar?.base64string
                  }}
                />
              </Badge>
            </div>
          ))}
        {isLoading && (
          <div>
            <div className='space-y-3'>
              {[...Array(4)].map((i, index) => (
                <div key={index} className='bg-default-50 hover:bg-default-100 rounded-md px-2 transition-all duration-150'>
                  <PartnerSkeleton />
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </>
  )
}
