{"name": "takenpass", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "type-check": "tsc --noEmit", "preview": "vite preview", "i18n:scan": "i18next-scanner --config i18next-scanner.config.cjs && node i18autoTranslate.cjs && node i18jsonToTS.cjs"}, "dependencies": {"@heroui/react": "^2.8.3", "@heroui/system": "^2.4.21", "@heroui/theme": "^2.4.21", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-slot": "^1.2.3", "@smastrom/react-rating": "^1.5.0", "@tabler/icons-react": "^3.34.1", "@tanstack/react-query": "^5.87.1", "@tanstack/react-query-devtools": "^5.87.3", "@tanstack/react-router": "^1.131.36", "@trpc/client": "^11.5.1", "@trpc/next": "^11.5.1", "@trpc/react-query": "^11.5.1", "@trpc/server": "^11.5.1", "@tsparticles/engine": "^3.9.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.9.1", "@uidotdev/usehooks": "^2.4.1", "better-auth": "^1.3.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.18", "dotenv": "^16.6.1", "dotted-map": "^2.2.3", "framer-motion": "^12.23.12", "i18next": "^23.16.8", "install": "^0.13.0", "leaflet": "^1.9.4", "lucide-react": "^0.543.0", "luxon": "^3.7.2", "motion": "^12.23.12", "query-string": "^9.2.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^14.1.3", "react-leaflet": "^4.2.1", "react-phone-number-input": "^3.4.12", "react-toastify": "^11.0.5", "socket.io-client": "^4.8.1", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "use-bus": "^2.5.2", "zustand": "^5.0.8"}, "devDependencies": {"@playwright/test": "^1.55.0", "@tailwindcss/postcss": "^4.1.13", "@tailwindcss/vite": "^4.1.13", "@types/leaflet": "^1.9.20", "@types/luxon": "^3.7.1", "@types/node": "^24.3.1", "@types/react": "^18.3.24", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^5.0.2", "autoprefixer": "^10.4.21", "bing-translate-api": "^4.1.0", "i18next-scanner": "^4.6.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.13", "typescript": "^5.9.2", "vite": "^7.1.5"}}