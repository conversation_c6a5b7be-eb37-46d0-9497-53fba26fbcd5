import { ChatPartners } from '@/components/chat/ChatPartners'
import { MainChat } from '@/components/chat/MainChat'
import { ChatPartner } from '@/types/chat'
import { ScrollShadow } from "@heroui/react"
import { useSearch } from '@tanstack/react-router'
import { useMediaQuery } from '@uidotdev/usehooks'
import { useState } from 'react'

export const ChatPage = () => {
  const [activeUser, setActiveUser] = useState<ChatPartner>()

  const searchParams = useSearch({ from: '/messages' })

  const isHorizontalOrientation = useMediaQuery('only screen and (max-width : 1020px)')

  return (
    <>
      <div className='lg:container mx-auto'>
        <div className='lg:flex items-start lg:space-x-10'>
          <div className='lg:w-[200px] lg:sticky top-16'>
            <ScrollShadow
              className='scrollbar scrollbar-h-2 scrollbar-thumb-zinc-300 scrollbar-track-zinc-100 dark:scrollbar-thumb-zinc-800 dark:scrollbar-track-zinc-700'
              hideScrollBar={false}
              offset={20}
              visibility='auto'
              orientation={isHorizontalOrientation ? 'horizontal' : 'vertical'}
            >
              <div className='mb-5'>
                <ChatPartners initUserId={searchParams?.partnerId} setUser={setActiveUser} />
              </div>
            </ScrollShadow>
          </div>
          <div className='w-full mt-5 lg:mt-0 lg:w-5/6'>
            <MainChat activeUser={activeUser} />
          </div>
        </div>
      </div>
    </>
  )
}
